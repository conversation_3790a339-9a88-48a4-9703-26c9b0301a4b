<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ProgressIndicator?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<VBox prefHeight="720.0" prefWidth="1000.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.MenuController">
  <children>
    <AnchorPane prefHeight="90.0" styleClass="side-menu" stylesheets="@../design/style.css">
      <children>
        <ScrollPane fitToHeight="true" hbarPolicy="AS_NEEDED" prefHeight="90.0" styleClass="scroll-pane" vbarPolicy="NEVER" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
          <content>
            <HBox fx:id="categoryBar" alignment="CENTER" spacing="15.0" style="-fx-background-color: white; -fx-padding: 15;">
              <padding>
                <Insets bottom="15.0" left="15.0" right="15.0" top="15.0" />
              </padding>
            </HBox>
          </content>
        </ScrollPane>
      </children>
    </AnchorPane>

    <Label fx:id="errorLabel" alignment="CENTER" prefHeight="20.0" style="-fx-text-fill: red;" textAlignment="CENTER">
      <font>
        <Font size="14.0" />
      </font>
      <VBox.margin>
        <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
      </VBox.margin>
    </Label>

    <StackPane VBox.vgrow="ALWAYS">
      <children>
        <ScrollPane fx:id="menuScrollPane" fitToWidth="true" prefHeight="600.0"> <content>
          <GridPane fx:id="menuGrid" alignment="TOP_CENTER" hgap="5.0" vgap="5.0">
            <padding>
              <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
            </padding>
          </GridPane>
        </content>
        </ScrollPane>
        <ProgressIndicator fx:id="loadingIndicator" maxHeight="100.0" maxWidth="100.0" prefHeight="100.0" prefWidth="100.0" stylesheets="@../design/style.css" visible="false" />
      </children>
    </StackPane>
  </children>
</VBox>
