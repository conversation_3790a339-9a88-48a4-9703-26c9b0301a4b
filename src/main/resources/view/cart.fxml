<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.shape.Line?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="720.0" prefWidth="1000.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.CartController">
  <children>
    <Label fx:id="cartLabel" layoutX="20.0" layoutY="14.0" prefHeight="28.0" prefWidth="640.0" textFill="RED">
      <font>
        <Font size="14.0" />
      </font>
    </Label>
    <ScrollPane fx:id="cartScrollPane" layoutX="10.0" layoutY="50.0" prefHeight="610.0" prefWidth="609.0">
      <content>
        <GridPane fx:id="cartGridPane" hgap="10.0" vgap="10.0" />
      </content>
    </ScrollPane>
    <AnchorPane layoutX="675.0" layoutY="171.0" prefHeight="252.0" prefWidth="310.0" styleClass="side-menu" stylesheets="@../design/style.css">
      <children>
        <Label layoutX="31.0" layoutY="24.0" prefHeight="30.0" prefWidth="218.0" style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Оформлення замовлення" />
        <Line endX="145.7999267578125" layoutX="139.0" layoutY="156.0" startX="-119.0" stroke="#9c8182" />

        <!-- Спосіб оплати -->
        <Label layoutX="36.0" layoutY="65.0" prefHeight="20.0" prefWidth="218.0" style="-fx-font-size: 12px; -fx-font-weight: bold;" text="Спосіб оплати:" />
        <ComboBox fx:id="paymentMethodComboBox" layoutX="36.0" layoutY="93.0" prefHeight="40.0" prefWidth="234.0" styleClass="text-fields" stylesheets="@../design/style.css" />

        <!-- Загальна сума -->
        <Label fx:id="totalAmountLabel" layoutX="30.0" layoutY="156.0" prefHeight="30.0" prefWidth="218.0" style="-fx-font-size: 14px; -fx-font-weight: bold;" text="Загальна сума: 0.00 грн" />

        <!-- Кнопка оформлення -->
        <Button fx:id="placeOrderButton" layoutX="37.0" layoutY="198.0" prefHeight="40.0" prefWidth="234.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Оформити замовлення" textFill="WHITE">
          <font>
            <Font size="14.0" />
          </font>
        </Button>
      </children>
    </AnchorPane>
      <Line endX="2.0" endY="315.0" layoutX="658.0" layoutY="294.0" startX="2.0" startY="-217.79998779296875" stroke="#e47d7e" strokeWidth="2.0" />
  </children>
</AnchorPane>
