<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.text.Font?>

<AnchorPane maxHeight="338" maxWidth="587.0" minHeight="-Infinity" minWidth="-Infinity" prefHeight="117.0" prefWidth="547.0" styleClass="menu-item-card" stylesheets="@../design/style.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.CartItemCard">
  <children>
    <ImageView fx:id="menuImage" fitHeight="100.0" fitWidth="100.0" layoutX="17.0" layoutY="8.0" preserveRatio="true">
      <image>

      </image>
    </ImageView>
    <Label fx:id="menuItemName" alignment="TOP_LEFT" layoutX="127.0" layoutY="20.0" prefHeight="30.0" prefWidth="226.0" style="-fx-wrap-text: true; -fx-font-size: 14px; -fx-font-weight: bold;" textAlignment="CENTER">
         <font>
            <Font name="System Bold" size="14.0" />
         </font></Label>
    <Label fx:id="menuItemPrice" alignment="CENTER" layoutX="353.0" layoutY="50.0" prefHeight="20.0" prefWidth="88.0" text="0.00 грн" textAlignment="CENTER">
         <font>
            <Font name="System Bold" size="12.0" />
         </font></Label>
    <Button fx:id="deleteFromCartButton" layoutX="466.0" layoutY="33.0" prefHeight="50.0" prefWidth="50.0" styleClass="close-button" stylesheets="@../design/style.css" textFill="WHITE">
      <font>
        <Font size="14.0" />
      </font>
         <graphic>
            <ImageView fitHeight="40.0" fitWidth="40.0" pickOnBounds="true" preserveRatio="true">
               <image>
                  <Image url="@../images/trash.png" />
               </image>
            </ImageView>
         </graphic>
    </Button>
    <Label fx:id="quantityLabel" alignment="TOP_LEFT" layoutX="137.0" layoutY="60.0" prefHeight="20.0" prefWidth="182.0" style="-fx-text-fill: #666666; -fx-font-size: 12px;" text="Кількість: " />
  </children>
</AnchorPane>
