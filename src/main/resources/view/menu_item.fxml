<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.CheckBox?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="720.0" prefWidth="1000.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.MenuItemController">

  <padding>
    <Insets bottom="10" left="10" right="10" top="10" />
  </padding>

  <!-- Пошук -->
  <HBox alignment="CENTER" layoutX="167.0" layoutY="20.0" spacing="10.0">
    <ImageView fitHeight="30.0" fitWidth="30.0" pickOnBounds="true" preserveRatio="true">
      <image>
        <Image url="@../images/search.png" />
      </image>
    </ImageView>
    <TextField fx:id="searchTextField" prefHeight="30.0" prefWidth="638.0" promptText="Введіть назву страви для пошуку" styleClass="search" stylesheets="@../design/style.css">
      <font>
        <Font size="14.0" />
      </font>
    </TextField>
  </HBox>

  <!-- Кнопки управління -->
  <HBox alignment="CENTER" layoutX="154.0" layoutY="248.0" prefHeight="70.0" prefWidth="472.0" spacing="10.0">
    <Button fx:id="addButton" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Додати">
      <font>
        <Font size="14.0" />
      </font>
    </Button>
    <Button fx:id="editButton" disable="true" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Редагувати">
      <font>
        <Font size="14.0" />
      </font>
    </Button>
    <Button fx:id="deleteButton" disable="true" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Видалити">
      <font>
        <Font size="14.0" />
      </font>
    </Button>
    <Button fx:id="clearFieldsButton" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Очистити">
      <font>
        <Font size="14.0" />
      </font>
    </Button>
  </HBox>

  <!-- Таблиця -->
  <TableView fx:id="menuItemTable" layoutX="51.0" layoutY="319.0" prefHeight="375.0" prefWidth="906.0" styleClass="table" stylesheets="@../design/style.css" tableMenuButtonVisible="true">
    <columns>
      <TableColumn fx:id="nameColumn" prefWidth="150" text="Назва" />
      <TableColumn fx:id="descriptionColumn" prefWidth="200" text="Опис" />
      <TableColumn fx:id="priceColumn" prefWidth="100" text="Ціна" />
      <TableColumn fx:id="categoryColumn" prefWidth="150" text="Категорія" />
      <TableColumn fx:id="availableColumn" prefWidth="124.79998779296875" text="Доступність" />
      <TableColumn fx:id="portionSizeColumn" prefWidth="150.40008544921875" text="Розмір порції" />
    </columns>
    <placeholder>
      <Label styleClass="label" stylesheets="@../design/style.css" text="Немає страв" />
    </placeholder>
  </TableView>

  <!-- Форма редагування -->
  <VBox layoutX="69.0" layoutY="144.0" prefHeight="110.0" prefWidth="876.0" spacing="15.0">
   
    <!-- Третій ряд - зображення -->
    <HBox alignment="CENTER_LEFT" prefHeight="80.0" prefWidth="876.0" spacing="20.0">
         <children>
         
                   <!-- Другий ряд - опис -->
          <VBox prefHeight="100.0" prefWidth="677.0" spacing="5.0">
            <Label styleClass="label" stylesheets="@../design/style.css" text="Опис страви:">
              <font>
                <Font size="14.0" />
              </font>
            </Label>
            <TextArea fx:id="descriptionField" prefHeight="76.0" prefWidth="677.0" stylesheets="@../design/style.css" wrapText="true">
              <font>
                <Font size="14.0" />
              </font>
            </TextArea>
          </VBox>
         <ImageView fx:id="imagePreview" fitHeight="100" fitWidth="100.0" preserveRatio="true" />
        <Button fx:id="chooseImageButton" prefHeight="39.0" prefWidth="157.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Зображення">
          <font>
            <Font size="14.0" />
          </font>
        </Button>
         </children>
    </HBox>

  </VBox>

 <!-- Перший ряд полів -->
 <HBox alignment="CENTER_LEFT" layoutX="87.0" layoutY="71.0" spacing="20.0">
   <VBox prefWidth="200.0" spacing="5.0">
     <Label styleClass="label" stylesheets="@../design/style.css" text="Назва страви:">
       <font>
         <Font size="14.0" />
       </font>
     </Label>
     <TextField fx:id="nameField" prefHeight="35.0" styleClass="text-fields" stylesheets="@../design/style.css">
       <font>
         <Font size="14.0" />
       </font>
     </TextField>
   </VBox>

   <VBox prefWidth="150.0" spacing="5.0">
     <Label styleClass="label" stylesheets="@../design/style.css" text="Ціна (₴):">
       <font>
         <Font size="14.0" />
       </font>
     </Label>
     <TextField fx:id="priceField" prefHeight="35.0" styleClass="text-fields" stylesheets="@../design/style.css">
       <font>
         <Font size="14.0" />
       </font>
     </TextField>
   </VBox>

   <VBox prefWidth="180.0" spacing="5.0">
     <Label styleClass="label" stylesheets="@../design/style.css" text="Категорія:">
       <font>
         <Font size="14.0" />
       </font>
     </Label>
     <ComboBox fx:id="categoryComboBox" prefHeight="35.0" prefWidth="180.0" styleClass="text-fields" stylesheets="@../design/style.css" />
   </VBox>

   <VBox prefWidth="150.0" spacing="5.0">
     <Label styleClass="label" stylesheets="@../design/style.css" text="Розмір порції:">
       <font>
         <Font size="14.0" />
       </font>
     </Label>
     <ComboBox fx:id="portionSizeComboBox" prefHeight="35.0" prefWidth="150.0" styleClass="text-fields" stylesheets="@../design/style.css" />
   </VBox>

   <VBox alignment="CENTER" spacing="5.0">
     <Label styleClass="label" stylesheets="@../design/style.css" text="Доступність:">
       <font>
         <Font size="14.0" />
       </font>
     </Label>
     <CheckBox fx:id="isAvailableCheckBox" styleClass="checkbox" stylesheets="@../design/style.css" text="Доступна" />
   </VBox>
 </HBox>

</AnchorPane>
