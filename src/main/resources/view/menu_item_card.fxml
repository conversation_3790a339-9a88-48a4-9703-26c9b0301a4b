<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Spinner?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.text.Font?>
<?import javafx.scene.text.Text?>

<AnchorPane prefHeight="220.0" prefWidth="320.0" styleClass="menu-item-card" stylesheets="@../design/style.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.MenuItemCardController">
  <children>
    <ImageView fx:id="itemImage" fitHeight="120.0" fitWidth="120.0" layoutX="14.0" layoutY="35.0" preserveRatio="true" styleClass="item-image">
         <image>
            <Image url="@../images/fast-food.jpg" />
         </image>
    </ImageView>

    <Text fx:id="itemName" layoutX="140.0" layoutY="35.0" styleClass="item-name" textAlignment="LEFT" wrappingWidth="165.0">
         <font>
            <Font name="System Bold" size="14.0" />
         </font>
    </Text>

    <Label fx:id="itemDescription" alignment="TOP_LEFT" layoutX="140.0" layoutY="45.0" prefHeight="50.0" prefWidth="165.0" text="Опис страви" textFill="#8e8383" wrapText="true">
         <font>
            <Font size="11.0" />
         </font>
    </Label>

    <ComboBox fx:id="portionSizeComboBox" layoutX="140.0" layoutY="100.0" prefHeight="26.0" prefWidth="165.0" promptText="Розмір порції" styleClass="portion-size-combobox" />

    <Text fx:id="itemPrice" layoutX="140.0" layoutY="160.0" styleClass="item-price">
         <font>
            <Font name="System Bold" size="14.0" />
         </font>
    </Text>

    <Spinner fx:id="quantity" layoutX="12.0" layoutY="170.0" prefHeight="35.0" prefWidth="90.0" styleClass="quantity-spinner" stylesheets="@../design/style.css" />

    <Button fx:id="addToCartButton" layoutX="111.0" layoutY="170.0" prefHeight="35.0" prefWidth="195.0" styleClass="add-to-cart-button" text="Додати до кошика">
         <graphic>
            <ImageView fitHeight="20.0" fitWidth="20.0" pickOnBounds="true" preserveRatio="true">
               <image>
                  <Image url="@../images/cart.png" />
               </image>
            </ImageView>
         </graphic>
         <font>
            <Font size="14.0" />
         </font>
    </Button>
  </children>
</AnchorPane>
