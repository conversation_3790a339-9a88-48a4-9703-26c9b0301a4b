<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.text.Font?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="720.0" prefWidth="1000.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.UserManagementController">
  <children>
    <AnchorPane layoutX="54.0" layoutY="61.0" prefHeight="181.0" prefWidth="870.0">
      <children>
        <Label layoutX="51.0" layoutY="68.0" styleClass="label" stylesheets="@../design/style.css" text="Email:">
          <font>
            <Font size="14.0" />
          </font></Label>
        <TextField fx:id="emailField" layoutX="139.0" layoutY="61.0" prefHeight="34.0" prefWidth="236.0" styleClass="text-fields" stylesheets="@../design/style.css">
          <font>
            <Font size="14.0" />
          </font></TextField>
        <Label layoutX="478.0" layoutY="25.0" styleClass="label" stylesheets="@../design/style.css" text="Пароль:">
          <font>
            <Font size="14.0" />
          </font></Label>
        <PasswordField fx:id="passwordField" layoutX="547.0" layoutY="18.0" prefHeight="34.0" prefWidth="236.0" styleClass="text-fields" stylesheets="@../design/style.css">
          <font>
            <Font size="14.0" />
          </font></PasswordField>
        <Label layoutX="14.0" layoutY="16.0" styleClass="label" stylesheets="@../design/style.css" text="Ім'я користувача:">
          <font>
            <Font size="14.0" />
          </font></Label>
        <TextField fx:id="usernameField" layoutX="139.0" layoutY="18.0" prefHeight="34.0" prefWidth="236.0" styleClass="text-fields" stylesheets="@../design/style.css">
          <font>
            <Font size="14.0" />
          </font></TextField>
        <Label layoutX="487.0" layoutY="68.0" styleClass="label" stylesheets="@../design/style.css" text="Роль:">
          <font>
            <Font size="14.0" />
          </font></Label>
        <ComboBox fx:id="roleComboBox" layoutX="547.0" layoutY="61.0" prefHeight="34.0" prefWidth="236.0" styleClass="text-fields" stylesheets="@../design/style.css" />
        <Button fx:id="addButton" layoutX="40.0" layoutY="126.0" mnemonicParsing="false" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Додати">
          <font>
            <Font size="14.0" />
          </font></Button>
        <Button fx:id="editButton" disable="true" layoutX="151.0" layoutY="126.0" mnemonicParsing="false" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Редагувати">
          <font>
            <Font size="14.0" />
          </font></Button>
        <Button fx:id="deleteButton" disable="true" layoutX="262.0" layoutY="126.0" mnemonicParsing="false" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Видалити">
          <font>
            <Font size="14.0" />
          </font></Button>
        <Button fx:id="clearFieldsButton" layoutX="374.0" layoutY="126.0" mnemonicParsing="false" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Очистити">
          <font>
            <Font size="14.0" />
          </font></Button>
      </children>
      <padding>
        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
      </padding>
    </AnchorPane>
    <HBox alignment="CENTER" layoutX="64.0" layoutY="14.0" prefHeight="47.0" prefWidth="887.0" spacing="10.0">
      <children>
        <ImageView fitHeight="25.0" fitWidth="25.0" pickOnBounds="true" preserveRatio="true">
          <image>
            <Image url="@../images/search.png" />
          </image>
        </ImageView>
        <TextField fx:id="searchTextField" prefHeight="26.0" prefWidth="805.0" styleClass="search" stylesheets="@../design/style.css" />
      </children>
      <padding>
        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
      </padding>
    </HBox>
    <TableView fx:id="userTable" editable="true" layoutX="26.0" layoutY="263.0" prefHeight="386.0" prefWidth="960.0" styleClass="table" stylesheets="@../design/style.css" tableMenuButtonVisible="true">
      <columns>
        <TableColumn fx:id="usernameColumn" prefWidth="278.4000358581543" text="Ім'я користувача" />
        <TableColumn fx:id="emailColumn" prefWidth="245.5999755859375" text="Email" />
        <TableColumn fx:id="roleColumn" prefWidth="210.4000244140625" text="Роль" />
        <TableColumn fx:id="createdAtColumn" prefWidth="202.4000244140625" text="Дата створення" />
      </columns>
      <placeholder>
        <Label styleClass="label" stylesheets="@../design/style.css" text="Немає користувачів" />
      </placeholder>
    </TableView>
  </children>
</AnchorPane>
