<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="400.0" prefWidth="500.0" styleClass="order-details-pane" stylesheets="@../design/style.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.OrderDetailsController">
  <VBox layoutX="20.0" layoutY="20.0" spacing="8.0" AnchorPane.LeftAnchor="20.0" AnchorPane.RightAnchor="20.0" AnchorPane.TopAnchor="20.0">
    <Label fx:id="orderIdLabel" text="ID замовлення:">
      <font>
        <Font name="System Bold" size="14.0" />
      </font>
    </Label>
    <Label fx:id="userIdLabel" text="Користувач:">
      <font>
        <Font size="13.0" />
      </font>
    </Label>
    <Label fx:id="totalPriceLabel" text="Загальна сума:">
      <font>
        <Font size="13.0" />
      </font>
    </Label>
    <Label fx:id="createdAtLabel" text="Дата створення:">
      <font>
        <Font size="13.0" />
      </font>
    </Label>
    <Label fx:id="statusLabel" text="Статус:">
      <font>
        <Font size="13.0" />
      </font>
    </Label>
  </VBox>

  <Label layoutX="20.0" layoutY="160.0" styleClass="items-label" text="Товари в замовленні:" AnchorPane.LeftAnchor="20.0">
    <font>
      <Font name="System Bold" size="14.0" />
    </font></Label>
  <TextArea fx:id="itemsTextArea" editable="false" layoutX="20.0" layoutY="185.0" prefHeight="150.0" wrapText="true" AnchorPane.BottomAnchor="65.0" AnchorPane.LeftAnchor="20.0" AnchorPane.RightAnchor="20.0" />

  <HBox layoutX="20.0" layoutY="350.0" spacing="10.0" alignment="CENTER" AnchorPane.BottomAnchor="20.0" AnchorPane.LeftAnchor="20.0" AnchorPane.RightAnchor="20.0">
    <Button fx:id="closeButton" onAction="#closeWindow" prefHeight="35.0" prefWidth="120.0" styleClass="details-button" stylesheets="@../design/style.css" text="Закрити">
      <font>
        <Font size="14.0" />
      </font>
    </Button>
    <Button fx:id="cancelOrderButton" onAction="#cancelOrder" prefHeight="35.0" prefWidth="120.0" styleClass="cancel-button" stylesheets="@../design/style.css" text="Скасувати">
      <font>
        <Font size="14.0" />
      </font>
    </Button>
  </HBox>
</AnchorPane>