<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="720.0" prefWidth="1000.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.CategoryController">
  <children>
    <VBox layoutX="455.0" layoutY="120.0" prefHeight="350.0" prefWidth="448.0" spacing="10.0">
      <children>
        <HBox alignment="CENTER" prefHeight="85.0" prefWidth="448.0" spacing="10.0">
          <children>
            <Label styleClass="label" stylesheets="@../design/style.css" text="Назва:">
              <font>
                <Font size="14.0" />
              </font>
            </Label>
            <TextField fx:id="nameField" prefHeight="41.0" prefWidth="313.0" promptText="Введіть назву категорії" styleClass="text-fields" stylesheets="@../design/style.css" />
          </children>
          <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
          </padding>
        </HBox>
        <HBox alignment="CENTER" prefHeight="101.0" prefWidth="394.0" spacing="10.0">
          <children>
            <Label styleClass="label" stylesheets="@../design/style.css" text="Зображення">
              <font>
                <Font size="14.0" />
              </font>
            </Label>
            <ImageView fx:id="imagePreview" fitHeight="90.0" fitWidth="90.0" pickOnBounds="true" preserveRatio="true" />
            <Button fx:id="chooseImageButton" prefHeight="38.0" prefWidth="114.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Вибрати" />
          </children>
          <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
          </padding>
        </HBox>
        <HBox alignment="CENTER" prefHeight="68.0" prefWidth="448.0" spacing="10.0">
          <children>
            <Button fx:id="addButton" mnemonicParsing="false" prefHeight="42.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Додати">
              <font>
                <Font size="14.0" />
              </font>
            </Button>
            <Button fx:id="editButton" disable="true" mnemonicParsing="false" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Редагувати">
              <font>
                <Font size="14.0" />
              </font>
            </Button>
            <Button fx:id="deleteButton" disable="true" mnemonicParsing="false" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Видалити">
              <font>
                <Font size="14.0" />
              </font>
            </Button>
            <Button fx:id="clearFieldsButton" mnemonicParsing="false" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Очистити">
              <font>
                <Font size="14.0" />
              </font>
            </Button>
          </children>
          <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
          </padding>
        </HBox>
      </children>
    </VBox>
    <TableView fx:id="categoryTable" editable="true" layoutX="41.0" layoutY="91.0" prefHeight="555.0" prefWidth="374.0" styleClass="table" stylesheets="@../design/style.css" tableMenuButtonVisible="true">
      <columns>
        <TableColumn fx:id="nameColumn" prefWidth="366.39997482299805" text="Назва" />
      </columns>
      <placeholder>
        <Label styleClass="label" stylesheets="@../design/style.css" text="Немає категорій" />
      </placeholder>
    </TableView>
    <HBox alignment="CENTER" layoutX="167.0" layoutY="20.0" spacing="10.0">
      <children>
            <ImageView fitHeight="30.0" fitWidth="30.0" pickOnBounds="true" preserveRatio="true">
               <image>
                  <Image url="@../images/search.png" />
               </image>
            </ImageView>
        <TextField fx:id="searchTextField" prefHeight="30.0" prefWidth="638.0" promptText="Введіть назву для пошуку" styleClass="search" stylesheets="@../design/style.css">
          <font>
            <Font size="14.0" />
          </font>
        </TextField>
      </children>
      <padding>
        <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
      </padding>
    </HBox>
  </children>
</AnchorPane>
