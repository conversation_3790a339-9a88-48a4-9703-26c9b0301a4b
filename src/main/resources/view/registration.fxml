<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.PasswordField?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.shape.Line?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="500.0" prefWidth="800.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.RegistrationController">
  <children>
    <AnchorPane layoutX="334.0" prefHeight="500.0" prefWidth="466.0">
      <children>
        <PasswordField fx:id="passwordField" layoutX="101.0" layoutY="248.0" prefHeight="42.0" prefWidth="263.0" promptText="Пароль" styleClass="text-fields" stylesheets="@../design/style.css">
          <font>
            <Font size="14.0" />
          </font></PasswordField>
        <TextField fx:id="loginField" layoutX="101.0" layoutY="132.0" prefHeight="42.0" prefWidth="263.0" promptText="Логін" styleClass="text-fields" stylesheets="@../design/style.css">
          <font>
            <Font size="14.0" />
          </font></TextField>
        <Button fx:id="signInButton" layoutX="101.0" layoutY="412.0" mnemonicParsing="false" prefHeight="42.0" prefWidth="263.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Авторизуватися" textFill="WHITE">
          <font>
            <Font size="16.0" />
          </font></Button>
        <Button fx:id="btnClose" layoutX="420.0" mnemonicParsing="false" prefHeight="18.0" prefWidth="32.0" styleClass="close-button" stylesheets="@../design/style.css">
          <graphic>
            <ImageView fitHeight="24.0" fitWidth="27.0" pickOnBounds="true" preserveRatio="true">
              <image>
                <Image url="@../images/close.png" />
              </image>
            </ImageView>
          </graphic>
        </Button>
        <Button fx:id="signUpButton" layoutX="101.0" layoutY="322.0" mnemonicParsing="false" prefHeight="42.0" prefWidth="263.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Зареєструватися" textFill="WHITE">
          <font>
            <Font size="16.0" />
          </font>
        </Button>
        <Line endX="-13.5999755859375" endY="3.0517578125E-5" layoutX="217.0" layoutY="391.0" startX="-110.0" startY="3.0517578125E-5" />
        <Label layoutX="214.0" layoutY="380.0" text="АБО">
          <font>
            <Font name="Georgia" size="18.0" />
          </font>
        </Label>
        <Line endX="-13.5999755859375" endY="3.0517578125E-5" layoutX="375.0" layoutY="391.0" startX="-110.0" startY="3.0517578125E-5" />
        <Label alignment="CENTER" layoutX="101.0" layoutY="14.0" prefHeight="102.0" prefWidth="263.0" style="-fx-wrap-text: true;" text="Система замовлення їжі" textAlignment="CENTER">
          <font>
            <Font name="Georgia" size="30.0" />
          </font>
        </Label>
            <TextField fx:id="emailField" layoutX="101.0" layoutY="192.0" prefHeight="42.0" prefWidth="263.0" promptText="Email" styleClass="text-fields" stylesheets="@../design/style.css">
               <font>
                  <Font size="14.0" />
               </font>
            </TextField>
      </children>
    </AnchorPane>
    <AnchorPane prefHeight="500.0" prefWidth="334.0">
      <children>
        <ImageView fitHeight="500.0" fitWidth="334.0" pickOnBounds="true" preserveRatio="true" style="-fx-opacity: 0.6;">
          <image>
            <Image url="@../images/auth.jpg" />
          </image>
        </ImageView>
        <AnchorPane prefHeight="500.0" prefWidth="333.0" style="-fx-background-color: black; -fx-opacity: 0.6;">
          <children>
            <Label layoutX="39.0" layoutY="56.0" text="Ласкаво просимо!" textFill="WHITE">
              <font>
                <Font name="System Bold" size="30.0" />
              </font>
            </Label>
            <Label layoutX="39.0" layoutY="102.0" prefHeight="60.0" prefWidth="238.0" text="Скучили за улюбленими смаками? Tap &amp; Eat усе просто: обирайте, замовляйте — і насолоджуйтесь! " textFill="WHITE" wrapText="true">
              <font>
                <Font name="Segoe UI" size="14.0" />
              </font>
            </Label>
          </children>
        </AnchorPane>
      </children>
    </AnchorPane>
  </children>
</AnchorPane>
