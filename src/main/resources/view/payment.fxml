<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="720.0" prefWidth="1000.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.PaymentController">

  <padding>
    <Insets bottom="10" left="10" right="10" top="10" />
  </padding>

  <!-- Пошук -->
  <HBox alignment="CENTER" layoutX="167.0" layoutY="20.0" spacing="10.0">
    <ImageView fitHeight="30.0" fitWidth="30.0" pickOnBounds="true" preserveRatio="true">
      <image>
        <Image url="@../images/search.png" />
      </image>
    </ImageView>
    <TextField fx:id="searchTextField" prefHeight="30.0" prefWidth="638.0" promptText="Введіть текст для пошуку" styleClass="search" stylesheets="@../design/style.css">
      <font>
        <Font size="14.0" />
      </font>
    </TextField>
  </HBox>

  <!-- Вибір методу оплати -->
  <HBox alignment="CENTER" layoutX="156.0" layoutY="94.0" spacing="10.0">
    <Label styleClass="label" stylesheets="@../design/style.css" text="Метод оплати:">
      <font>
        <Font size="14.0" />
      </font>
    </Label>
    <ComboBox fx:id="paymentMethodComboBox" prefHeight="42.0" prefWidth="239.0" promptText="Виберіть метод оплати" styleClass="text-fields" stylesheets="@../design/style.css" />
  </HBox>

  <!-- Вибір статусу -->
  <HBox alignment="CENTER" layoutX="507.0" layoutY="94.0" spacing="10.0">
    <Label styleClass="label" stylesheets="@../design/style.css" text="Статус платежу:">
      <font>
        <Font size="14.0" />
      </font>
    </Label>
    <ComboBox fx:id="paymentStatusComboBox" prefHeight="42.0" prefWidth="235.0" promptText="Виберіть статус" styleClass="text-fields" stylesheets="@../design/style.css" />
  </HBox>

  <!-- Кнопки -->
  <HBox alignment="CENTER" layoutX="167.0" layoutY="166.0" spacing="10.0">
    <Button fx:id="editButton" disable="true" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Редагувати">
      <font>
        <Font size="14.0" />
      </font>
    </Button>
    <Button fx:id="deleteButton" disable="true" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Видалити">
      <font>
        <Font size="14.0" />
      </font>
    </Button>
    <Button fx:id="clearFieldsButton" prefHeight="40.0" prefWidth="100.0" styleClass="side-btn" stylesheets="@../design/style.css" text="Очистити">
      <font>
        <Font size="14.0" />
      </font>
    </Button>
  </HBox>

  <!-- Таблиця -->
  <TableView fx:id="paymentTable" layoutX="63.0" layoutY="254.0" prefHeight="460.0" prefWidth="876.0" styleClass="table" stylesheets="@../design/style.css" tableMenuButtonVisible="true">
    <columns>
      <TableColumn fx:id="cartIdColumn" prefWidth="232.8" text="ID кошика" />
      <TableColumn fx:id="paymentMethodColumn" prefWidth="233.6" text="Метод оплати" />
      <TableColumn fx:id="paymentStatusColumn" prefWidth="168.0" text="Статус платежу" />
      <TableColumn fx:id="createdAtColumn" prefWidth="216.0" text="Дата створення" />
    </columns>
    <placeholder>
      <Label styleClass="label" stylesheets="@../design/style.css" text="Немає платежів" />
    </placeholder>
  </TableView>

</AnchorPane>
