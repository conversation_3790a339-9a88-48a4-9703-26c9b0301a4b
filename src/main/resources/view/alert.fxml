<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.Button?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="102.0" prefWidth="463.0" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.metenkanich.fastfoodkiosk.presentation.controller.AlertController">
  <children>
    <Button fx:id="closeButton" layoutX="402.0" layoutY="30.0" mnemonicParsing="false" prefHeight="40.0" prefWidth="40.0" styleClass="close-button" stylesheets="@../design/style.css">
         <graphic>
            <ImageView fitHeight="30.0" fitWidth="30.0" pickOnBounds="true" preserveRatio="true">
               <image>
                  <Image url="@../images/close.png" />
               </image>
            </ImageView>
         </graphic></Button>
  <Label fx:id="messageLabel" alignment="CENTER" layoutX="29.0" layoutY="6.0" prefHeight="106.0" prefWidth="374.0" style="-fx-font-size: 16px;" textAlignment="CENTER" wrapText="true">
    <font>
      <Font name="Georgia" size="12.0" />
    </font>
      </Label>
      <AnchorPane layoutX="-11.0" prefHeight="116.0" prefWidth="24.0" style="-fx-background-color: #e47d7e;" />
  </children>
</AnchorPane>
