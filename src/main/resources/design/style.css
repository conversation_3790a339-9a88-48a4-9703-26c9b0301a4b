
.side-menu {
  -fx-background-color: white;
  -fx-background-radius: 20 20 0 0;
}
.side-btn {
  -fx-background-color: #e47d7e;
  -fx-background-radius: 24px;
  -fx-alignment: CENTER;
  -fx-cursor: hand;
  -fx-padding: 0 10px 0 10px;
}

.text-fields {
  -fx-background-color: transparent;
  -fx-border-color: #000000;
  -fx-border-radius: 24px;
  -fx-padding: 0 10px 0 10px;
}
.close-button {
  -fx-background-color: transparent;
  -fx-cursor: hand;
}

.close-button:hover {
  -fx-background-color: #ff0000;
  -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0.2, 0, 2);
}

.minimize-button {
  -fx-background-color: transparent;
  -fx-cursor: hand;
}

.minimize-button:hover {
  -fx-background-color: rgba(255, 255, 255, 0.2);
}

.image-view {
  -fx-border-color: #e47d7e;
  -fx-border-width: 2;
  -fx-border-radius: 50%;
  -fx-background-radius: 50%;
}
.menu-button {
  -fx-background-color: transparent;
  -fx-alignment: CENTER_LEFT;
  -fx-padding: 0 10px 0 10px;
  -fx-cursor: hand;
}

.menu-item-card {
  -fx-background-color: #ffffff;
  -fx-background-radius: 15;
  -fx-border-radius: 15;
  -fx-border-color: rgba(228, 125, 126, 0.1);
  -fx-border-width: 1;
  -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 8, 0.3, 0, 3);
  -fx-alignment: center;
}

.menu-item-card:hover {
  -fx-effect: dropshadow(gaussian, rgba(228, 125, 126, 0.2), 12, 0.4, 0, 5);
  -fx-border-color: rgba(228, 125, 126, 0.3);
}

.portion-size-box {
  -fx-background-color: #ecf0f1;
  -fx-background-radius: 10;
  -fx-padding: 10;
  -fx-spacing: 5;
  -fx-alignment: center;
}

.availability-label {
  -fx-font-size: 12px;
  -fx-text-fill: #ffffff;
  -fx-background-color: #28a745;
  -fx-background-radius: 10;
  -fx-padding: 4 10 4 10;
  -fx-font-weight: bold;
  -fx-font-family: "Segoe UI", Arial, sans-serif;
  -fx-effect: dropshadow(gaussian, rgba(40, 167, 69, 0.2), 3, 0.2, 0, 1);
}

.quantity-spinner {
  -fx-background-color: #ffffff;
 }

.quantity-spinner .increment-arrow-button,
.quantity-spinner .decrement-arrow-button {
  -fx-cursor: hand;
}

.quantity-spinner .increment-arrow-button:hover,
.quantity-spinner .decrement-arrow-button:hover {
  -fx-background-color: #d65f60;
}

.add-to-cart-button {
  -fx-background-color: #e47d7e;
  -fx-text-fill: white;
  -fx-background-radius: 25;
  -fx-cursor: hand;
  -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 5, 0.3, 0, 2);
}

.add-to-cart-button:hover {
  -fx-background-color: #d65f60;
  -fx-effect: dropshadow(gaussian, rgba(211, 84, 0, 0.3), 8, 0.4, 0, 3);
}

.add-to-cart-button:pressed {
  -fx-background-color: #d65f60;
  -fx-effect: dropshadow(gaussian, rgba(176, 58, 46, 0.2), 5, 0.2, 0, 2);
}

.details-button {
  -fx-background-color: #e47d7e;
  -fx-text-fill: white;
  -fx-background-radius: 25;
  -fx-cursor: hand;
  -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 5, 0.3, 0, 2);
}

.details-button:hover {
  -fx-background-color: #d65f60;
  -fx-effect: dropshadow(gaussian, rgba(211, 84, 0, 0.3), 8, 0.4, 0, 3);
}

.details-button:pressed {
  -fx-background-color: #d65f60;
  -fx-effect: dropshadow(gaussian, rgba(176, 58, 46, 0.2), 5, 0.2, 0, 2);
}

.cancel-button {
  -fx-background-color: #dc3545;
  -fx-text-fill: white;
  -fx-background-radius: 25;
  -fx-cursor: hand;
  -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 5, 0.3, 0, 2);
}

.cancel-button:hover {
  -fx-background-color: #c82333;
  -fx-effect: dropshadow(gaussian, rgba(200, 35, 51, 0.3), 8, 0.4, 0, 3);
}

.cancel-button:pressed {
  -fx-background-color: #bd2130;
  -fx-effect: dropshadow(gaussian, rgba(189, 33, 48, 0.2), 5, 0.2, 0, 2);
}

.cancel-button:disabled {
  -fx-background-color: #6c757d;
  -fx-text-fill: #adb5bd;
  -fx-cursor: default;
  -fx-effect: none;
}

.progress-indicator .percentage {
  -fx-fill: #e47d7e;
}

.progress-indicator .spinner {
  -fx-border-color: #e47d7e;
}

.progress-indicator {
  -fx-outer-border: transparent;
  -fx-progress-color: #e47d7e;
}


.table {
  -fx-border-color: #e0e0e0;
  -fx-border-width: 1px;
  -fx-font-size: 14px;
  -fx-font-family: "Arial";
}

.table .column-header-background {
  -fx-background-color: #e47d7e;
  -fx-border-color: #e0e0e0;
  -fx-border-width: 0 0 1px 0;
}

.table .column-header .label {
  -fx-font-weight: bold;
  -fx-alignment: CENTER;
}

.table .table-row-cell {
  -fx-border-color: #e0e0e0;
  -fx-border-width: 0 0 1px 0;
  -fx-alignment: CENTER;
}

.table .table-row-cell:hover {
  -fx-background-color: #f0f0f0;
}

.table .table-row-cell:selected {
  -fx-background-color: #e47d7e;
  -fx-text-fill: #ffffff;
}

.table .table-cell {
  -fx-alignment: CENTER;
}

.search {
  -fx-background-color: transparent;
  -fx-border-color: #000000;
  -fx-border-width: 0px 0px .4px 0px;
}
